var table;
var baseAjaxLink, lastCallLanguageParam;
const WarrantyDetailsCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }

        // Used to calculate the default order based on last change
        var totalColumns = document.querySelector('.datatable thead tr:first-child').cells.length;

        // Visibility button default class
        $.fn.dataTable.Buttons.defaults.dom.button.className = 'btn';

        // Date format for sorting
        $.fn.dataTable.moment('DD/MM/YYYY');

        // Basic setup
        table = $('.datatable-basic').DataTable({
            autoWidth: false,
            columnDefs: [
                {
                    orderable: false,
                    targets: [0, totalColumns - 2, totalColumns - 1]
                },
                {
                    width: 20,
                    targets: [0, totalColumns - 1]
                },
                {
                    className: "text-center",
                    targets: [totalColumns - 2]
                }
            ],
            order: [[totalColumns - 3, 'desc']],
            dom: '<"datatable-header"fl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {
                search: '<span class="me-3">Filtra:</span> <div class="form-control-feedback form-control-feedback-end flex-fill">_INPUT_<div class="form-control-feedback-icon"><i class="ph-magnifying-glass opacity-50"></i></div></div>',
                searchPlaceholder: 'Digita per filtrare...',
                lengthMenu: '<span class="me-3">Mostra:</span> _MENU_',
                paginate: { 'first': 'First', 'last': 'Last', 'next': $('html').attr('dir') == 'rtl' ? '&larr;' : '&rarr;', 'previous': $('html').attr('dir') == 'rtl' ? '&rarr;' : '&larr;' }
            },
            ajax: {
                url: getRoute('BE_WARRANTYDETAILS_DATA'),
                type: 'POST'
            },
            drawCallback: function () {
                $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
            },
            preDrawCallback: function() {
                $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
            }
        });

        // Alternative pagination
        $('.datatable-pagination').DataTable({
            pagingType: "simple",
            language: {
                paginate: {'next': $('html').attr('dir') == 'rtl' ? 'Prev' : 'Next', 'previous': $('html').attr('dir') == 'rtl' ? 'Next' : 'Prev'}
            }
        });

        // Datatable with saving state
        $('.datatable-save-state').DataTable({
            stateSave: true,
            language: {
                search: '<span class="me-3">Filtra:</span> <div class="form-control-feedback form-control-feedback-end flex-fill">_INPUT_<div class="form-control-feedback-icon"><i class="ph-magnifying-glass opacity-50"></i></div></div>',
                lengthMenu: '<span class="me-3">Mostra:</span> _MENU_',
                paginate: { 'first': 'First', 'last': 'Last', 'next': $('html').attr('dir') == 'rtl' ? '&larr;' : '&rarr;', 'previous': $('html').attr('dir') == 'rtl' ? '&rarr;' : '&larr;' }
            }
        });

        // Resize scrollable table when sidebar width is changed
        $('body').on('click', '.sidebar-control', function() {
            table.columns.adjust().draw();
        });
    };

    // Select/deselect all checkboxes
    const _componentCheckboxes = function() {
        // Select all checkbox action
        $('.datatable tbody').on('change', 'input[type="checkbox"]', function() {
            if(this.checked) {
                $(this).closest('tr').addClass('table-warning');
            }
            else {
                $(this).closest('tr').removeClass('table-warning');
            }
        });

        // Master checkbox action
        $('.datatable thead input[type="checkbox"], .datatable tfoot input[type="checkbox"]').on('change', function() {
            var set = $(this).attr("data-set");
            var checked = this.checked;
            $(set).each(function() {
                if (checked) {
                    this.checked = true;
                    $(this).closest('tr').addClass('table-warning');
                }
                else {
                    this.checked = false;
                    $(this).closest('tr').removeClass('table-warning');
                }
            });
        });
    };

    // Bulk actions
    const _componentBulkActions = function() {
        // Show bulk actions modal
        $(document).on('click', '.btn-bulk-actions', function() {
            var checkedBoxes = $('.datatable tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length > 0) {
                $('#modal_bulk_actions').modal('show');
            } else {
                new Noty({
                    text: 'Seleziona almeno un elemento per eseguire azioni multiple.',
                    type: 'warning'
                }).show();
            }
        });
    };

    // Execute bulk action
    const executeBulkAction = function() {
        var operation = $('#bulk_operation').val();
        if (!operation) {
            new Noty({
                text: 'Seleziona un\'azione da eseguire.',
                type: 'warning'
            }).show();
            return;
        }

        var checkedBoxes = $('.datatable tbody input[type="checkbox"]:checked');
        var ids = [];
        checkedBoxes.each(function() {
            ids.push($(this).val());
        });

        if (ids.length === 0) {
            new Noty({
                text: 'Nessun elemento selezionato.',
                type: 'warning'
            }).show();
            return;
        }

        // Confirm action
        var confirmMessage = 'Sei sicuro di voler eseguire questa azione su ' + ids.length + ' elemento/i?';
        if (operation === 'delete') {
            confirmMessage = 'Sei sicuro di voler eliminare ' + ids.length + ' elemento/i? Questa azione non può essere annullata.';
        }

        if (confirm(confirmMessage)) {
            $.ajax({
                url: getRoute('BE_WARRANTYDETAILS_OPERATE'),
                type: 'POST',
                data: {
                    operation: operation,
                    warrantyDetailsIds: ids.join(',')
                },
                success: function(response) {
                    $('#modal_bulk_actions').modal('hide');
                    table.ajax.reload();
                    new Noty({
                        text: 'Azione eseguita con successo.',
                        type: 'success'
                    }).show();
                },
                error: function() {
                    new Noty({
                        text: 'Errore durante l\'esecuzione dell\'azione.',
                        type: 'error'
                    }).show();
                }
            });
        }
    };

    // Return objects assigned to module
    return {
        init: function() {
            _componentDatatable();
            _componentCheckboxes();
            _componentBulkActions();
        },
        executeBulkAction: executeBulkAction
    }
}();

// Initialize module
document.addEventListener('DOMContentLoaded', function() {
    WarrantyDetailsCollection.init();
});
