package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import dao.BusinessDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.StatusType;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class HomeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HomeController.class.getName());

    public static Route error_page = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        response.redirect(RoutesUtils.contextPath(request) + Routes.ERROR_404);
        return "ok";
    };

    public static TemplateViewRoute error_404 = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ERROR_404, attributes, request);
    };

    public static TemplateViewRoute home = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes);
        String language = RoutesUtils.language(request);
        
        if (user == null) {
            response.redirect(RoutesUtils.getLocalizedFullPath(request, "COMING_SOON", language));
        }

        // i più votati
        List<Bson> filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.CONFIRMED.name().toLowerCase()));
        filters.add(DaoFilters.getFilter("editorChoice", DaoFiltersOperation.EQ, true));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 4, "creation", "desc");
        List<Business> editorChoices = BaseDao.getDocumentsByFilters(Business.class, queryOptions, false);
        attributes.put("editorChoices", editorChoices);

        // progetti
        filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.PUBLISHED.name().toLowerCase()));
        queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 4, "creation", "desc");
        List<Project> projects = BaseDao.getDocumentsByFilters(Project.class, queryOptions, language);
        attributes.put("projects", projects);

        // last business (9)
        filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.CONFIRMED.name().toLowerCase()));
        queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 9, "creation", "desc");
        List<Business> lastBusinesses = BaseDao.getDocumentsByFilters(Business.class, queryOptions, false);
        attributes.put("lastBusinesses", lastBusinesses);

        // blog (4)
        filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.PUBLISHED.name().toLowerCase()));
        queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 4, "creation", "desc");
        List<Article> articles = BaseDao.getDocumentsByFilters(Article.class, queryOptions, language);
        attributes.put("articles", articles);

        // business preferiti
        if (user != null) {
            filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
            queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<BusinessPreferred> businessPreferreds = BaseDao.getDocumentsByFilters(BusinessPreferred.class, queryOptions, false);
            // Map them for id
            Map<ObjectId, BusinessPreferred> businessPreferredMap = new HashMap<>();
            for (BusinessPreferred businessPreferred : businessPreferreds) {
                businessPreferredMap.put(businessPreferred.getBusinessId(), businessPreferred);
            }
            attributes.put("businessPreferreds", businessPreferredMap);
        }

        // lista città per search
        List<String> cities = BusinessDao.getDistinctCities();
        attributes.put("cities", cities);

        return Core.render(Pages.HOME, attributes, request);
    };
    
    public static TemplateViewRoute coming_soon = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.COMING_SOON, attributes, request);
    };

    public static TemplateViewRoute about = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ABOUT, attributes, request);
    };
    
    public static TemplateViewRoute menu = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.MENU, attributes, request);
    };
    
    public static TemplateViewRoute services = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.SERVICES, attributes, request);
    };

    public static TemplateViewRoute contacts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.CONTACTS, attributes, request);
    };
    
    public static TemplateViewRoute job = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.JOB, attributes, request);
    };

    public static TemplateViewRoute photo_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.PHOTO_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute photo_category = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // identifier
        String category = request.params("category");
        attributes.put("category", category);
        
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.PHOTO_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute photo = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Photo photo = null;
        if (StringUtils.isNotBlank(identifier)) {
            photo = BaseDao.getDocumentByIdentifier(identifier, Photo.class, attributes.get("language").toString());
        }

        attributes.put("photo", photo);

        return Core.render(Pages.PHOTO, attributes, request);
    };

    public static TemplateViewRoute news_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.NEWS_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute news = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Article news = null;
        if (StringUtils.isNotBlank(identifier)) {
            news = BaseDao.getDocumentByIdentifier(identifier, Article.class, attributes.get("language").toString());
        }

        attributes.put("news", news);

        return Core.render(Pages.NEWS, attributes, request);
    };
    
}
