package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.InsuranceCompany;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class InsuranceCompanyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InsuranceCompanyController.class.getName());

    public static TemplateViewRoute be_insurancecompany_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_INSURANCECOMPANY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_insurancecompany = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("insuranceCompanyId"));
        if (oid != null) {
            InsuranceCompany loadedInsuranceCompany = BaseDao.getDocumentById(oid, InsuranceCompany.class);
            attributes.put("curInsuranceCompany", loadedInsuranceCompany);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                InsuranceCompany loadedInsuranceCompany = BaseDao.getDocumentByParentId(parentId, InsuranceCompany.class);
                if (loadedInsuranceCompany != null) {
                    attributes.put("curInsuranceCompany", loadedInsuranceCompany);
                }
            }
        }

        return Core.render(Pages.BE_INSURANCECOMPANY, attributes, request);
    };

    public static Route be_insurancecompany_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<InsuranceCompany> loadedInsuranceCompanies;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedInsuranceCompanies = BaseDao.getDocumentsByFilters(InsuranceCompany.class, queryOptions, loadArchived);
        } else {
            loadedInsuranceCompanies = BaseDao.getDocumentsByFilters(InsuranceCompany.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedInsuranceCompanies.isEmpty()) {
            for (InsuranceCompany tmpInsuranceCompany : loadedInsuranceCompanies) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' insuranceCompanyId='").append(tmpInsuranceCompany.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_INSURANCECOMPANY).append("?insuranceCompanyId=").append(tmpInsuranceCompany.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpInsuranceCompany.getDescription(), "N.D.")).append("</a>\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpInsuranceCompany.getCode(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpInsuranceCompany.getExternalCode(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpInsuranceCompany.getEndpoint(), "N.D.")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpInsuranceCompany.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpInsuranceCompany.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_insurancecompany_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("insuranceCompanyId"));
        InsuranceCompany newInsuranceCompany;
        if (oid != null) {
            newInsuranceCompany = BaseDao.getDocumentById(oid, InsuranceCompany.class);
            RequestUtils.mergeFromParams(params, newInsuranceCompany);
        } else {
            newInsuranceCompany = RequestUtils.createFromParams(params, InsuranceCompany.class);
        }

        if (newInsuranceCompany != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newInsuranceCompany);
                newInsuranceCompany.setId(oid);

                BaseDao.insertLog(user, newInsuranceCompany, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newInsuranceCompany);
                BaseDao.insertLog(user, newInsuranceCompany, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newInsuranceCompany, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newInsuranceCompany, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_insurancecompany_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String insuranceCompanyIds = params.get("insuranceCompanyIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(insuranceCompanyIds)) {
            String[] ids = insuranceCompanyIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    InsuranceCompany tmpInsuranceCompany = BaseDao.getDocumentById(oid, InsuranceCompany.class);
                    if (tmpInsuranceCompany != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpInsuranceCompany);
                                BaseDao.insertLog(user, tmpInsuranceCompany, LogType.DELETE);
                                break;
                            case "archive":
                                tmpInsuranceCompany.setArchived(true);
                                BaseDao.updateDocument(tmpInsuranceCompany);
                                BaseDao.insertLog(user, tmpInsuranceCompany, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpInsuranceCompany.setArchived(false);
                                BaseDao.updateDocument(tmpInsuranceCompany);
                                BaseDao.insertLog(user, tmpInsuranceCompany, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
