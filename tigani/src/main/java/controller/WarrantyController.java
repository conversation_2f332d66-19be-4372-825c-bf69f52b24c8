package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Warranty;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class WarrantyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyController.class.getName());

    public static TemplateViewRoute be_warranty_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_WARRANTY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_warranty = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyId"));
        if (oid != null) {
            Warranty loadedWarranty = BaseDao.getDocumentById(oid, Warranty.class);
            attributes.put("curWarranty", loadedWarranty);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Warranty loadedWarranty = BaseDao.getDocumentByParentId(parentId, Warranty.class);
                if (loadedWarranty != null) {
                    attributes.put("curWarranty", loadedWarranty);
                }
            }
        }

        return Core.render(Pages.BE_WARRANTY, attributes, request);
    };

    public static Route be_warranty_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Warranty> loadedWarranties;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedWarranties = BaseDao.getDocumentsByFilters(Warranty.class, queryOptions, loadArchived);
        } else {
            loadedWarranties = BaseDao.getDocumentsByFilters(Warranty.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedWarranties.isEmpty()) {
            for (Warranty tmpWarranty : loadedWarranties) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' warrantyId='").append(tmpWarranty.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_WARRANTY).append("?warrantyId=").append(tmpWarranty.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpWarranty.getTitle(), "N.D.")).append("</a>\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpWarranty.getCode(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpWarranty.getDescription(), "N.D.")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarranty.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarranty.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_warranty_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyId"));
        Warranty newWarranty;
        if (oid != null) {
            newWarranty = BaseDao.getDocumentById(oid, Warranty.class);
            RequestUtils.mergeFromParams(params, newWarranty);
        } else {
            newWarranty = RequestUtils.createFromParams(params, Warranty.class);
        }

        if (newWarranty != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newWarranty);
                newWarranty.setId(oid);

                BaseDao.insertLog(user, newWarranty, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newWarranty);
                BaseDao.insertLog(user, newWarranty, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newWarranty, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newWarranty, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_warranty_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String warrantyIds = params.get("warrantyIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(warrantyIds)) {
            String[] ids = warrantyIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Warranty tmpWarranty = BaseDao.getDocumentById(oid, Warranty.class);
                    if (tmpWarranty != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpWarranty);
                                BaseDao.insertLog(user, tmpWarranty, LogType.DELETE);
                                break;
                            case "archive":
                                tmpWarranty.setArchived(true);
                                BaseDao.updateDocument(tmpWarranty);
                                BaseDao.insertLog(user, tmpWarranty, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpWarranty.setArchived(false);
                                BaseDao.updateDocument(tmpWarranty);
                                BaseDao.insertLog(user, tmpWarranty, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
