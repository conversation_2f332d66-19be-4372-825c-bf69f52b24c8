package pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Channel extends BasePojo {

    private String code;
    private List<ObjectId> brandModelIds;
    private List<ObjectId> warrantyIds;
    private List<ObjectId> insuranceProvenanceTypeIds;
    private Integer capacityLimit;
    private ObjectId companyId;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<ObjectId> getBrandModelIds() {
        return brandModelIds;
    }

    public void setBrandModelIds(List<ObjectId> brandModelIds) {
        this.brandModelIds = brandModelIds;
    }

    public List<ObjectId> getWarrantyIds() {
        return warrantyIds;
    }

    public void setWarrantyIds(List<ObjectId> warrantyIds) {
        this.warrantyIds = warrantyIds;
    }

    public List<ObjectId> getInsuranceProvenanceTypeIds() {
        return insuranceProvenanceTypeIds;
    }

    public void setInsuranceProvenanceTypeIds(List<ObjectId> insuranceProvenanceTypeIds) {
        this.insuranceProvenanceTypeIds = insuranceProvenanceTypeIds;
    }

    public Integer getCapacityLimit() {
        return capacityLimit;
    }

    public void setCapacityLimit(Integer capacityLimit) {
        this.capacityLimit = capacityLimit;
    }

    public ObjectId getCompanyId() {
        return companyId;
    }

    public void setCompanyId(ObjectId companyId) {
        this.companyId = companyId;
    }
}
