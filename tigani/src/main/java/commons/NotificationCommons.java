package commons;

import com.mitchellbosecke.pebble.PebbleEngine;
import com.mitchellbosecke.pebble.loader.StringLoader;
import dao.BaseDao;
import dao.MailDao;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import extensions.Extensions;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Mailtemplate;
import pojo.Smtp;
import spark.Spark;
import utils.GoogleRecaptchaValidator;
import utils.MailUtils;
import utils.RequestUtils;

/**
 * Common utilities for notification and email sending functionality
 * 
 * <AUTHOR>
 */
public class NotificationCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationCommons.class.getName());

    /**
     * Sends an email using a mail template with optional recaptcha validation
     * 
     * @param params Map containing email parameters (type, language, recaptchaToken, etc.)
     * @param validateRecaptcha Whether to validate recaptcha token or not
     * @param saveContact Whether to save contact information to database
     * @return true if email was sent successfully, false otherwise
     * @throws Exception if there are validation errors or email sending fails
     */
    public static boolean sendTemplatedEmail(Map<String, Object> params, boolean validateRecaptcha, boolean saveContact) throws Exception {
        String to = null, object = null, content = null, language;
        Smtp smtp = null;
        boolean validMail = false;
        
        // Validate recaptcha if required
        if (validateRecaptcha) {
            if (!params.containsKey("recaptchaToken")) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
            }
            if (!GoogleRecaptchaValidator.isValid(params.get("recaptchaToken").toString())) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
            }
        }
        
        // Validate required parameters
        if (!params.containsKey("type")) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "missing type parameter");
        }
        
        String type = params.get("type").toString();
        language = params.get("language").toString();
        
        if (StringUtils.isBlank(language)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid language");
        }
        
        /*if (!EnumUtils.isValidEnum(MailType.class, type)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid type template");
        }*/
        
        // Load mail template
        Mailtemplate template = MailDao.getMailtemplateByKey(type, language);
        
        if (template == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid template");
        }
        
        // Prepare email data
        to = StringUtils.join(template.getTo(), ",");
        object = template.getObject();

        if (params.containsKey("to")) {
            to = params.get("to").toString();
        }
        
        if (template.getSmtpId() != null) {
            smtp = BaseDao.getDocumentById(template.getSmtpId(), Smtp.class);
        }
        
        // Process template content with Pebble engine
        /*Map<String, Object> templateAttributes = new HashMap<>();
        for (String attribute : params.keySet()) {
            templateAttributes.put(attribute, params.get(attribute));
        }*/
        
        StringWriter writer = new StringWriter();
        PebbleEngine pebble = new PebbleEngine.Builder()
                .loader(new StringLoader())
                .defaultLocale(Locale.ITALIAN)
                .extension(new Extensions())
                .cacheActive(true)
                .strictVariables(false)
                .build();
        pebble.getTemplate(template.getDescription()).evaluate(writer, params);
        content = writer.toString();
        
        // Validate email data
        if (StringUtils.isNotBlank(to) && StringUtils.isNotBlank(object)
                && StringUtils.isNotBlank(content) && smtp != null) {
            validMail = true;
        }
        
        if (validMail) {
            // Send email
            MailUtils.sendMail(to, object, content, null, smtp, template);
            
            // Save contact if requested
            if (saveContact) {
                saveContactRecord(params, object, content, type);
            }
            
            return true;
        } else {
            LOGGER.warn("Can't send the mail, one or more parameters are invalid");
            return false;
        }
    }
    
    /**
     * Saves contact information to database
     * 
     * @param params Map containing contact parameters
     * @param subject Email subject
     * @param mailContent Email content
     * @param templateType Template type used
     */
    private static void saveContactRecord(Map<String, Object> params, String subject, String mailContent, String templateType) {
        try {
            Contact contact = new Contact();
            Map<String, String> stringParams = new HashMap<>();
            for (String key : params.keySet()) {
                if (params.get(key) instanceof String) {
                    stringParams.put(key, (String) params.get(key));
                }
            }
            RequestUtils.mergeFromParams(stringParams, contact);
            
            contact.setSubject(subject);
            contact.setTemplate(templateType);
            contact.setMail(mailContent);
            
            BaseDao.insertDocument(contact);
        } catch (Exception ex) {
            LOGGER.error("unable save contact, exception is: ", ex);
        }
    }
    
    /**
     * Overloaded method with default parameters for backward compatibility
     * 
     * @param params Map containing email parameters
     * @param validateRecaptcha Whether to validate recaptcha token or not
     * @return true if email was sent successfully, false otherwise
     * @throws Exception if there are validation errors or email sending fails
     */
    public static boolean sendTemplatedEmail(Map<String, Object> params, boolean validateRecaptcha) throws Exception {
        return sendTemplatedEmail(params, validateRecaptcha, true);
    }
}
