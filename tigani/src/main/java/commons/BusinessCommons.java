package commons;

import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

import java.util.ArrayList;
import java.util.List;

public class BusinessCommons {

    public static List<BusinessEntry> toEntries(List<Business> businesses) throws Exception {
        List<BusinessEntry> entries = new ArrayList<>();
        for (Business business : businesses) {
            entries.add(toEntry(business));
        }
        return entries;
    }

    public static BusinessEntry toEntry(Business business) throws Exception {
        BusinessEntry entry = new BusinessEntry();
        entry.setBusiness(business);

        List<Bson> filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("businessId", DaoFiltersOperation.EQ, business.getId()));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
        BusinessAnalytics businessAnalytics = BaseDao.getDocumentByFilters(BusinessAnalytics.class, queryOptions);
        if (businessAnalytics != null) {
            entry.setAnalytics(businessAnalytics);
        } else {
            BusinessAnalytics newAnalytics = new BusinessAnalytics();
            newAnalytics.setViews(0);
            newAnalytics.setFollowers(0);
            newAnalytics.setMessages(0);
            newAnalytics.setMessagesNotRead(0);
            entry.setAnalytics(newAnalytics);
        }

        return entry;
    }
}
