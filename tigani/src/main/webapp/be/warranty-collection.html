{% extends "be/include/base.html" %}

{% set page = 'WARRANTY' %}

{% block extrahead %}
<title>Garanzie</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/warranty-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_WARRANTY_DATA', '{{ routes("BE_WARRANTY_DATA") }}');
addRoute('BE_WARRANTY_OPERATE', '{{ routes("BE_WARRANTY_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Basic datatable -->
    <div class="card">
        <div class="card-header d-flex align-items-center">
            <h5 class="mb-0">G<PERSON><PERSON></h5>
            <div class="d-inline-flex ms-auto">
                <a href="{{ routes('BE_WARRANTY') }}" class="btn btn-primary">
                    <i class="ph-plus me-2"></i>
                    Aggiungi Garanzia
                </a>
            </div>
        </div>

        <div class="card-body">
            <table class="table datatable-basic">
                <thead>
                    <tr>
                        <th width="20">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="checkbox_all">
                                <label class="form-check-label" for="checkbox_all"></label>
                            </div>
                        </th>
                        <th>Titolo</th>
                        <th>Codice</th>
                        <th>Descrizione</th>
                        <th>Creazione</th>
                        <th>Ultimo Aggiornamento</th>
                        <th class="text-center">Azioni</th>
                        <th width="20"></th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <!-- /basic datatable -->

</div>
<!-- /content area -->

<!-- Bulk actions modal -->
<div id="modal_bulk_actions" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Azioni Multiple</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <p>Seleziona l'azione da eseguire sugli elementi selezionati:</p>
                
                <div class="mb-3">
                    <label class="form-label">Azione:</label>
                    <select id="bulk_operation" class="form-control">
                        <option value="">Seleziona azione...</option>
                        <option value="delete">Elimina</option>
                        <option value="archive">Archivia</option>
                        <option value="unarchive">Ripristina</option>
                    </select>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="WarrantyCollection.executeBulkAction()">Esegui</button>
            </div>
        </div>
    </div>
</div>
<!-- /bulk actions modal -->

{% endblock %}
