{% extends "be/include/base.html" %}

{% set page = 'WARRANTYDETAILS' %}
{% set title = curWarrantyDetails is empty ? 'Nuovo Dettaglio Garanzia' : 'Modifica Dettaglio Garanzia' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/warrantydetails.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_WARRANTYDETAILS', '{{ routes("BE_WARRANTYDETAILS") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curWarrantyDetails is empty %}
            <h5 class="mb-0">Inserisci Dettaglio Garanzia</h5>
            {% else %}
            <h5 class="mb-0">Modifica Dettaglio Garanzia</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_WARRANTYDETAILS_SAVE') %}
            {% if curWarrantyDetails.id is not empty %}                
            {% set postUrl = routes('BE_WARRANTYDETAILS_SAVE') + '?warrantyDetailsId=' + curWarrantyDetails.id %}
            {% endif %}

            <form id="warrantydetails-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Garanzia: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="warrantyId" class="form-control select" data-placeholder="Seleziona garanzia" required>
                            <option value=""></option>
                            {% set warranties = lookup('Warranty', checkPublished=false, language='false') %}
                            {% if warranties is not empty %}
                                {% for warranty in warranties %}
                                    <option value="{{ warranty.id }}" {{ curWarrantyDetails.warrantyId equals warranty.id ? 'selected' : '' }}>{{ warranty.title }} ({{ warranty.code }})</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona la garanzia di riferimento.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice Provincia:</label>
                    <div class="col-lg-9">
                        <input name="provinceCode" type="text" class="form-control form-control-maxlength" placeholder="Codice provincia" value="{{ curWarrantyDetails.provinceCode }}" maxlength="10">
                        <div class="form-text text-muted">Codice della provincia (es. MI, RM, NA).</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Numero Sinistri:</label>
                    <div class="col-lg-9">
                        <input name="claimNumber" type="number" class="form-control" placeholder="Numero di sinistri" value="{{ curWarrantyDetails.claimNumber }}" min="0">
                        <div class="form-text text-muted">Numero di sinistri registrati.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipo Provenienza Assicurativa:</label>
                    <div class="col-lg-9">
                        <select name="insuranceProvenanceTypeId" class="form-control select" data-placeholder="Seleziona tipo provenienza">
                            <option value=""></option>
                            {% set insuranceProvenanceTypes = lookup('InsuranceProvenanceType', checkPublished=false, language='false') %}
                            {% if insuranceProvenanceTypes is not empty %}
                                {% for insuranceProvenanceType in insuranceProvenanceTypes %}
                                    <option value="{{ insuranceProvenanceType.id }}" {{ curWarrantyDetails.insuranceProvenanceTypeId equals insuranceProvenanceType.id ? 'selected' : '' }}>{{ insuranceProvenanceType.name }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona il tipo di provenienza assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Classe Universale:</label>
                    <div class="col-lg-9">
                        <input name="universalClass" type="number" class="form-control" placeholder="Classe universale" value="{{ curWarrantyDetails.universalClass }}" min="1" max="18">
                        <div class="form-text text-muted">Classe universale di merito (da 1 a 18).</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Valore Premio:</label>
                    <div class="col-lg-9">
                        <input name="premiumValue" type="number" step="0.01" class="form-control" placeholder="Valore del premio" value="{{ curWarrantyDetails.premiumValue }}" min="0">
                        <div class="form-text text-muted">Valore del premio assicurativo in euro.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_WARRANTYDETAILS_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curWarrantyDetails is empty %}
                        Crea Dettaglio
                        {% else %}
                        Aggiorna Dettaglio
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
