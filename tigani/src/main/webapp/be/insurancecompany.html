{% extends "be/include/base.html" %}

{% set page = 'INSURANCECOMPANY' %}
{% set title = curInsuranceCompany is empty ? 'Nuova Compagnia Assicurativa' : 'Modifica Compagnia Assicurativa' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/insurancecompany.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_INSURANCECOMPANY', '{{ routes("BE_INSURANCECOMPANY") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curInsuranceCompany is empty %}
            <h5 class="mb-0">Inserisci Compagnia Assicurativa</h5>
            {% else %}
            <h5 class="mb-0">Modifica Compagnia Assicurativa</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_INSURANCECOMPANY_SAVE') %}
            {% if curInsuranceCompany.id is not empty %}                
            {% set postUrl = routes('BE_INSURANCECOMPANY_SAVE') + '?insuranceCompanyId=' + curInsuranceCompany.id %}
            {% endif %}

            <form id="insurancecompany-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="code" type="text" class="form-control" placeholder="Codice identificativo" value="{{ curInsuranceCompany.code }}" required>
                        <div class="form-text text-muted">Codice univoco per identificare la compagnia assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice Esterno:</label>
                    <div class="col-lg-9">
                        <input name="externalCode" type="text" class="form-control" placeholder="Codice esterno" value="{{ curInsuranceCompany.externalCode }}">
                        <div class="form-text text-muted">Codice utilizzato da sistemi esterni.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Descrizione: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="description" type="text" class="form-control" placeholder="Nome della compagnia" value="{{ curInsuranceCompany.description }}" required>
                        <div class="form-text text-muted">Nome completo della compagnia assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Endpoint API:</label>
                    <div class="col-lg-9">
                        <input name="endpoint" type="url" class="form-control" placeholder="https://api.compagnia.com" value="{{ curInsuranceCompany.endpoint }}">
                        <div class="form-text text-muted">URL dell'endpoint API della compagnia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">API Key:</label>
                    <div class="col-lg-9">
                        <input name="apiKey" type="password" class="form-control" placeholder="Chiave API" value="{{ curInsuranceCompany.apiKey }}">
                        <div class="form-text text-muted">Chiave di autenticazione per l'API.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_INSURANCECOMPANY_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curInsuranceCompany is empty %}
                        Crea Compagnia
                        {% else %}
                        Aggiorna Compagnia
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
